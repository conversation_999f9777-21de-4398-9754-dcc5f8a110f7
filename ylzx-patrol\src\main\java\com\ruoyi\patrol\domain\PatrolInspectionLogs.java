package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡查日志对象 patrol_inspection_logs
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="巡查日志")
@TableName("patrol_inspection_logs")
@Data
public class PatrolInspectionLogs extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 巡查类型（1-日常巡查，2-夜间巡查） */
    @Excel(name = "巡查类型", readConverterExp = "1=日常巡查,2=夜间巡查")
    @ApiModelProperty(value = "巡查类型（1-日常巡查，2-夜间巡查）")
    private Integer patrolType;

    /** 养护路段ID */
//    @Excel(name = "养护路段ID")
    @ApiModelProperty(value = "养护路段ID")
    private String maintenanceSectionId;

    /** 养护路段名称 */
    @Excel(name = "养护路段名称")
    @ApiModelProperty(value = "养护路段名称")
    private String maintenanceSectionName;

    /** 路线编码 */
    @Excel(name = "路线编码")
    @ApiModelProperty(value = "路线编码")
    private String routeCode;

    /** 管养单位ID */
//    @Excel(name = "管养单位ID")
    @ApiModelProperty(value = "管养单位ID")
    private String maintenanceUnitId;

    /** 管养单位名称 */
    @Excel(name = "管养单位名称")
    @ApiModelProperty(value = "管养单位名称")
    private String maintenanceUnitName;


    /** 巡查方向（0-上行，1-下行，2-双向） */
    @Excel(name = "巡查方向", readConverterExp = "0=上行,1=下行,2=双向")
    @ApiModelProperty(value = "巡查方向（0-上行，1-下行，2-双向）")
    private Integer direction;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @ApiModelProperty(value = "车牌号")
    private String carNum;

    /** 天气 */
    @Excel(name = "天气")
    @ApiModelProperty(value = "天气")
    private String weather;

    /** 巡查内容 */
    @Excel(name = "巡查内容")
    @ApiModelProperty(value = "巡查内容")
    private String content;

    /** 巡查开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "巡查开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "巡查开始时间")
    private Date startTime;

    /** 巡查结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "巡查结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "巡查结束时间")
    private Date endTime;

    /** 上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上报时间")
    private Date reportedTime;

    /** 采集时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "采集时间")
    private Date collectTime;

    /** 巡查状态（0-正在巡查，1-完成巡查） */
//    @Excel(name = "巡查状态", readConverterExp = "0=-正在巡查，1-完成巡查")
    @ApiModelProperty(value = "巡查状态（0-正在巡查，1-完成巡查）")
    private Integer status;

    /** 巡查里程（km） */
    @Excel(name = "巡查里程（km）")
    @ApiModelProperty(value = "巡查里程（km）")
    private BigDecimal patrolMileage;


    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "记录人（老系统数据）")
    private String personStr;

//    @Excel(name = "巡查机构ID")
    @ApiModelProperty(value = "巡查机构ID")
    private Long patrolUnitId;

    @Excel(name = "巡查机构名称")
    @ApiModelProperty(value = "巡查机构名称）")
    private String patrolUnitName;

    /** 巡查用户ID */
    @ApiModelProperty(value = "巡查用户")
    @TableField(exist = false)
    private List<Long> userIds;

    @TableField(exist = false)
    private List<String> userNameList;

    @TableField(exist = false)
    private String userIdStr;

    @Excel(name = "巡查人员", type = Excel.Type.EXPORT)
    @TableField(exist = false)
    private String userNames;

    @TableField(exist = false)
    private String signIds;

    @TableField(exist = false)
    private List<String> signIdList;

    @TableField(exist = false)
    private List<String> nickNameList;

    @TableField(exist = false)
    private String nickNames;

    @TableField(exist = false)
    private List<LocalDate> dateList;

    @TableField(exist = false)
    private String ownerAvatar;


    @TableField(exist = false)
    private Integer diseaseNum;

    @TableField(exist = false)
    private List<RemoteRoadDiseaseResponse> roadDiseaseList;

    /** 用户签名URL列表 */
    @TableField(exist = false)
    private List<String> userSignUrlList;

    /** 用户签名URL字符串（逗号分隔） */
    @TableField(exist = false)
    private String signUrlStr;

    @TableField(exist = false)
    private List<String> signNameList;

    public List<Long> getUserIds() {
        if (userIds != null && !userIds.isEmpty()) {
            return userIds;
        }
        if (userIdStr == null || userIdStr.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(userIdStr.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public List<String> getUserNameList() {
        if (userNameList != null && !userNameList.isEmpty()) {
            return userNameList;
        }
        if (userNames == null || userNames.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(userNames.split(","));
    }

    public String getUserIdStr(){
        if(userIdStr != null && !userIdStr.isEmpty()){
            return userIdStr;
        }
        if(userIds == null || userIds.isEmpty()){
            return "";
        }
        return userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    public String getUserNames(){
        if(userNames != null && !userNames.isEmpty()){
            return userNames;
        }
        if(userNameList == null || userNameList.isEmpty()){
            return "";
        }
        return String.join(",", userNameList);
    }

    public String getNickNames(){
        if(nickNames != null && !nickNames.isEmpty()){
            return nickNames;
        }
        if(userNameList == null || userNameList.isEmpty()){
            return "";
        }
        return String.join(",", userNameList);
    }

   public List<String> getSignIdList() {
        if (signIdList != null && !signIdList.isEmpty()) {
            return signIdList;
        }
        if (signIds == null || signIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(signIds.split(","));
    }

    public String getSignIds(){
        if(signIds != null && !signIds.isEmpty()){
            return signIds;
        }
        if(signIdList == null || signIdList.isEmpty()){
            return "";
        }
        return String.join(",", signIdList);
    }

    public List<String> getSignNameList() {
        return signNameList;
    }

    public void setSignNameList(List<String> signNameList) {
        this.signNameList = signNameList;
    }

}
